[2025-08-11 17:38:09] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08111738_jxEaIY"}} 
[2025-08-11 17:38:10] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"<PERSON>","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas <PERSON>","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:10] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:10] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:11] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:11] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:11] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:11] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:12] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:12] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:12] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:12] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:13] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:13] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:13] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:14] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:14] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:14] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:14] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:15] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:15] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:15] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:15] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:16] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:16] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:16] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:16] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:17] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:17] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:17] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:38:17] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:27] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08111745_I5uBEt"}} 
[2025-08-11 17:45:27] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:28] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:28] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:28] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:28] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:29] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:29] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:29] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:30] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:30] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:30] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:30] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:31] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:31] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:31] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:31] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:32] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:32] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:32] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:32] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:33] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:33] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:33] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:33] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:34] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:34] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:34] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:35] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:35] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:45:35] local.INFO: r----> {"id":1,"title":"Essence Mascara Lash Princess","description":"The Essence Mascara Lash Princess is a popular mascara known for its volumizing and lengthening effects. Achieve dramatic lashes with this long-lasting and cruelty-free formula.","category":"beauty","price":9.99,"discountPercentage":10.48,"rating":2.56,"stock":99,"tags":["beauty","mascara"],"brand":"Essence","sku":"BEA-ESS-ESS-001","weight":4,"dimensions":{"width":15.14,"height":13.08,"depth":22.99},"warrantyInformation":"1 week warranty","shippingInformation":"Ships in 3-5 business days","availabilityStatus":"In Stock","reviews":[{"rating":3,"comment":"Would not recommend!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"},{"rating":4,"comment":"Very satisfied!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Lucas Gordon","reviewerEmail":"<EMAIL>"},{"rating":5,"comment":"Highly impressed!","date":"2025-04-30T09:41:02.053Z","reviewerName":"Eleanor Collins","reviewerEmail":"<EMAIL>"}],"returnPolicy":"No return policy","minimumOrderQuantity":48,"meta":{"createdAt":"2025-04-30T09:41:02.053Z","updatedAt":"2025-04-30T09:41:02.053Z","barcode":"5784719087687","qrCode":"https://cdn.dummyjson.com/public/qr-code.png"},"images":["https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/1.webp"],"thumbnail":"https://cdn.dummyjson.com/product-images/beauty/essence-mascara-lash-princess/thumbnail.webp"} 
[2025-08-11 17:46:20] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08111746_416GqW"}} 
[2025-08-11 17:46:23] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08111746_Q8zqj3"}} 
[2025-08-11 17:54:40] local.ERROR: The use statement with non-compound name 'Exception' has no effect {"exception":"[object] (ErrorException(code: 0): The use statement with non-compound name 'Exception' has no effect at C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php:3)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\
outes.php(26): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'The use stateme...', 'C:\\\\laragon8\\\\www...', 3, Array)
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\
outes.php(26): include_once()
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(83): require('C:\\\\laragon8\\\\www...')
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\ModuleServiceProvider.php(48): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\laragon8\\\\www...')
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\ModuleServiceProvider.php(37): App\\Modules\\ModuleServiceProvider->registerModule('CollectDebt')
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Modules\\ModuleServiceProvider->boot()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(37): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(596): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(867): Illuminate\\Container\\Container->call(Array)
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(850): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Modules\\ModuleServiceProvider))
#12 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Modules\\ModuleServiceProvider), 24)
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): array_walk(Array, Object(Closure))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(151): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(135): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#20 {main}
"} 
[2025-08-11 17:55:21] local.ERROR: The use statement with non-compound name 'Exception' has no effect {"exception":"[object] (ErrorException(code: 0): The use statement with non-compound name 'Exception' has no effect at C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php:3)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\
outes.php(26): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'The use stateme...', 'C:\\\\laragon8\\\\www...', 3, Array)
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\
outes.php(26): include_once()
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(83): require('C:\\\\laragon8\\\\www...')
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\ModuleServiceProvider.php(48): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\laragon8\\\\www...')
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\ModuleServiceProvider.php(37): App\\Modules\\ModuleServiceProvider->registerModule('CollectDebt')
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Modules\\ModuleServiceProvider->boot()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(37): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(596): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(867): Illuminate\\Container\\Container->call(Array)
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(850): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Modules\\ModuleServiceProvider))
#12 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Modules\\ModuleServiceProvider), 24)
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): array_walk(Array, Object(Closure))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(230): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(151): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(135): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#20 {main}
"} 
[2025-08-11 17:55:33] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111755_MixxKp"}} 
[2025-08-11 17:55:33] local.ERROR: Each value yielded by the iterator must be a Psr7\Http\Message\RequestInterface or a callable that returns a promise that fulfills with a Psr7\Message\Http\ResponseInterface object. {"exception":"[object] (InvalidArgumentException(code: 0): Each value yielded by the iterator must be a Psr7\\Http\\Message\\RequestInterface or a callable that returns a promise that fulfills with a Psr7\\Message\\Http\\ResponseInterface object. at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php:62)
[stacktrace]
#0 [internal function]: GuzzleHttp\\Pool::GuzzleHttp\\{closure}()
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\EachPromise.php(81): Generator->rewind()
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php(75): GuzzleHttp\\Promise\\EachPromise->promise()
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(45): GuzzleHttp\\Pool->promise()
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#31 {main}
"} 
[2025-08-11 17:56:47] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111756_zxBI1p"}} 
[2025-08-11 17:56:47] local.ERROR: Each value yielded by the iterator must be a Psr7\Http\Message\RequestInterface or a callable that returns a promise that fulfills with a Psr7\Message\Http\ResponseInterface object. {"exception":"[object] (InvalidArgumentException(code: 0): Each value yielded by the iterator must be a Psr7\\Http\\Message\\RequestInterface or a callable that returns a promise that fulfills with a Psr7\\Message\\Http\\ResponseInterface object. at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php:62)
[stacktrace]
#0 [internal function]: GuzzleHttp\\Pool::GuzzleHttp\\{closure}()
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\EachPromise.php(81): Generator->rewind()
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php(75): GuzzleHttp\\Promise\\EachPromise->promise()
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(45): GuzzleHttp\\Pool->promise()
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#31 {main}
"} 
[2025-08-11 17:57:18] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111757_tbFu2p"}} 
[2025-08-11 17:57:29] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111757_FqfhVJ"}} 
[2025-08-11 17:58:02] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111758_x3t8KR"}} 
[2025-08-11 17:58:47] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111758_OxYrDa"}} 
[2025-08-11 18:13:38] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111813_Vg4EHy"}} 
[2025-08-11 18:13:38] local.ERROR: Errorr {"exception":"[object] (Exception(code: 0): Errorr at C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php:12)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 {main}
"} 
[2025-08-11 18:13:53] local.INFO: DebtRecoveryRequestFinishCutOffTime {"request":{"api_request_id":"DEBT_08111813_zxZTzn"}} 
[2025-08-11 18:13:54] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111813_IMcSiA"}} 
[2025-08-11 18:13:54] local.ERROR: Loi gi eo biet {"exception":"[object] (Exception(code: 0): Loi gi eo biet at C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php:12)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 {main}
"} 
[2025-08-11 18:33:43] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111833_Nq8ejF"}} 
[2025-08-11 18:34:03] local.ERROR: SQLSTATE[HY000] [2002] A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
 (SQL: select * from `debt_recovery_request` where `payment_method_code` = MPOS and `status_payment` = 5 limit 10) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
 (SQL: select * from `debt_recovery_request` where `payment_method_code` = MPOS and `status_payment` = 5 limit 10) at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:671)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(631): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(339): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2270): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2258): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2753): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2259): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(548): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(532): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(18): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
 at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('mysql:host=10.2...', 'nextlend', 'k2Mgr0dczP9K,0h...', Array)
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(46): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=10.2...', 'nextlend', 'k2Mgr0dczP9K,0h...', Array)
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=10.2...', Array, Array)
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): call_user_func(Object(Closure))
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(405): Illuminate\\Database\\Connection->getReadPdo()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(331): Illuminate\\Database\\Connection->getPdoForSelect(true)
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(664): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(631): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(339): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2270): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2258): Illuminate\\Database\\Query\\Builder->runSelect()
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2753): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2259): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(548): Illuminate\\Database\\Query\\Builder->get(Array)
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(532): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(18): Illuminate\\Database\\Eloquent\\Builder->get()
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 {main}
"} 
[2025-08-11 18:34:23] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111834_GTJnue"}} 
[2025-08-11 18:34:27] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111834_z5JwNL"}} 
[2025-08-11 18:34:43] local.ERROR: SQLSTATE[HY000] [2002] A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
 (SQL: select * from `debt_recovery_request` where `payment_method_code` = MPOS and `status_payment` = 5 limit 10) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
 (SQL: select * from `debt_recovery_request` where `payment_method_code` = MPOS and `status_payment` = 5 limit 10) at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:671)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(631): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(339): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2270): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2258): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2753): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2259): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(548): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(532): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(18): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond.
 at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('mysql:host=10.2...', 'nextlend', 'k2Mgr0dczP9K,0h...', Array)
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(46): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=10.2...', 'nextlend', 'k2Mgr0dczP9K,0h...', Array)
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=10.2...', Array, Array)
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): call_user_func(Object(Closure))
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(405): Illuminate\\Database\\Connection->getReadPdo()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(331): Illuminate\\Database\\Connection->getPdoForSelect(true)
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(664): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(631): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(339): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2270): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2258): Illuminate\\Database\\Query\\Builder->runSelect()
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2753): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2259): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(548): Illuminate\\Database\\Query\\Builder->get(Array)
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(532): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(18): Illuminate\\Database\\Eloquent\\Builder->get()
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#44 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#45 {main}
"} 
[2025-08-11 18:37:53] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111837_Xdbmxw"}} 
[2025-08-11 18:39:17] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111839_cRZmST"}} 
[2025-08-11 18:39:17] local.ERROR: Argument 1 passed to GuzzleHttp\Psr7\Uri::__construct() must be of the type string, null given, called in C:\laragon8\www\nextpay-web\api-request-debt\vendor\guzzlehttp\psr7\src\Request.php on line 44 {"exception":"[object] (TypeError(code: 0): Argument 1 passed to GuzzleHttp\\Psr7\\Uri::__construct() must be of the type string, null given, called in C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Request.php on line 44 at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Uri.php:80)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Request.php(44): GuzzleHttp\\Psr7\\Uri->__construct(NULL)
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebtGateway\\Repositories\\Connections\\NextLendServiceConnection.php(143): GuzzleHttp\\Psr7\\Request->__construct('POST', NULL, Array, '{\"Fnc\":\"checkDe...')
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(35): App\\Modules\\CollectDebtGateway\\Repositories\\Connections\\NextLendServiceConnection->buildHttpRequest(Array, 'checkDebt')
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php(56): Illuminate\\Support\\ServiceProvider->{closure}()
#4 [internal function]: GuzzleHttp\\Pool::GuzzleHttp\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\EachPromise.php(81): Generator->rewind()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php(75): GuzzleHttp\\Promise\\EachPromise->promise()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(51): GuzzleHttp\\Pool->promise()
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#35 {main}
"} 
[2025-08-11 18:40:06] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111840_gj2w0F"}} 
[2025-08-11 18:40:24] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111840_a3cQ1H"}} 
[2025-08-11 18:40:25] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111840_o57osW"}} 
[2025-08-11 18:41:13] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111841_ldgGuE"}} 
[2025-08-11 18:41:30] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111841_yaaC0E"}} 
[2025-08-11 18:41:30] local.ERROR: Argument 1 passed to GuzzleHttp\Psr7\Uri::__construct() must be of the type string, null given, called in C:\laragon8\www\nextpay-web\api-request-debt\vendor\guzzlehttp\psr7\src\Request.php on line 44 {"exception":"[object] (TypeError(code: 0): Argument 1 passed to GuzzleHttp\\Psr7\\Uri::__construct() must be of the type string, null given, called in C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Request.php on line 44 at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Uri.php:80)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Request.php(44): GuzzleHttp\\Psr7\\Uri->__construct(NULL)
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebtGateway\\Repositories\\Connections\\NextLendServiceConnection.php(143): GuzzleHttp\\Psr7\\Request->__construct('POST', NULL, Array, '{\"Fnc\":\"checkDe...')
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(40): App\\Modules\\CollectDebtGateway\\Repositories\\Connections\\NextLendServiceConnection->buildHttpRequest(Array, 'checkDebt')
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php(56): Illuminate\\Support\\ServiceProvider->{closure}()
#4 [internal function]: GuzzleHttp\\Pool::GuzzleHttp\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\EachPromise.php(81): Generator->rewind()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php(75): GuzzleHttp\\Promise\\EachPromise->promise()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(56): GuzzleHttp\\Pool->promise()
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#35 {main}
"} 
[2025-08-11 18:41:51] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111841_eDVoSY"}} 
[2025-08-11 18:41:51] local.ERROR: Argument 1 passed to GuzzleHttp\Psr7\Uri::__construct() must be of the type string, null given, called in C:\laragon8\www\nextpay-web\api-request-debt\vendor\guzzlehttp\psr7\src\Request.php on line 44 {"exception":"[object] (TypeError(code: 0): Argument 1 passed to GuzzleHttp\\Psr7\\Uri::__construct() must be of the type string, null given, called in C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Request.php on line 44 at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Uri.php:80)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Request.php(44): GuzzleHttp\\Psr7\\Uri->__construct(NULL)
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebtGateway\\Repositories\\Connections\\NextLendServiceConnection.php(143): GuzzleHttp\\Psr7\\Request->__construct('POST', NULL, Array, '{\"Fnc\":\"checkDe...')
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(40): App\\Modules\\CollectDebtGateway\\Repositories\\Connections\\NextLendServiceConnection->buildHttpRequest(Array, 'checkDebt')
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php(56): Illuminate\\Support\\ServiceProvider->{closure}()
#4 [internal function]: GuzzleHttp\\Pool::GuzzleHttp\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\EachPromise.php(81): Generator->rewind()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php(75): GuzzleHttp\\Promise\\EachPromise->promise()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(56): GuzzleHttp\\Pool->promise()
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#35 {main}
"} 
[2025-08-11 18:42:00] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111842_zY0uuV"}} 
[2025-08-11 18:42:01] local.ERROR: Argument 1 passed to GuzzleHttp\Psr7\Uri::__construct() must be of the type string, null given, called in C:\laragon8\www\nextpay-web\api-request-debt\vendor\guzzlehttp\psr7\src\Request.php on line 44 {"exception":"[object] (TypeError(code: 0): Argument 1 passed to GuzzleHttp\\Psr7\\Uri::__construct() must be of the type string, null given, called in C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Request.php on line 44 at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Uri.php:80)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Request.php(44): GuzzleHttp\\Psr7\\Uri->__construct(NULL)
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebtGateway\\Repositories\\Connections\\NextLendServiceConnection.php(143): GuzzleHttp\\Psr7\\Request->__construct('POST', NULL, Array, '{\"Fnc\":\"checkDe...')
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(40): App\\Modules\\CollectDebtGateway\\Repositories\\Connections\\NextLendServiceConnection->buildHttpRequest(Array, 'checkDebt')
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php(56): Illuminate\\Support\\ServiceProvider->{closure}()
#4 [internal function]: GuzzleHttp\\Pool::GuzzleHttp\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\EachPromise.php(81): Generator->rewind()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php(75): GuzzleHttp\\Promise\\EachPromise->promise()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(56): GuzzleHttp\\Pool->promise()
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#35 {main}
"} 
[2025-08-11 18:42:02] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111842_qjsabO"}} 
[2025-08-11 18:42:02] local.ERROR: Argument 1 passed to GuzzleHttp\Psr7\Uri::__construct() must be of the type string, null given, called in C:\laragon8\www\nextpay-web\api-request-debt\vendor\guzzlehttp\psr7\src\Request.php on line 44 {"exception":"[object] (TypeError(code: 0): Argument 1 passed to GuzzleHttp\\Psr7\\Uri::__construct() must be of the type string, null given, called in C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Request.php on line 44 at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Uri.php:80)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Request.php(44): GuzzleHttp\\Psr7\\Uri->__construct(NULL)
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebtGateway\\Repositories\\Connections\\NextLendServiceConnection.php(143): GuzzleHttp\\Psr7\\Request->__construct('POST', NULL, Array, '{\"Fnc\":\"checkDe...')
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(40): App\\Modules\\CollectDebtGateway\\Repositories\\Connections\\NextLendServiceConnection->buildHttpRequest(Array, 'checkDebt')
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php(56): Illuminate\\Support\\ServiceProvider->{closure}()
#4 [internal function]: GuzzleHttp\\Pool::GuzzleHttp\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\EachPromise.php(81): Generator->rewind()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php(75): GuzzleHttp\\Promise\\EachPromise->promise()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(56): GuzzleHttp\\Pool->promise()
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#35 {main}
"} 
[2025-08-11 18:42:03] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111842_FJKop1"}} 
[2025-08-11 18:42:03] local.ERROR: Argument 1 passed to GuzzleHttp\Psr7\Uri::__construct() must be of the type string, null given, called in C:\laragon8\www\nextpay-web\api-request-debt\vendor\guzzlehttp\psr7\src\Request.php on line 44 {"exception":"[object] (TypeError(code: 0): Argument 1 passed to GuzzleHttp\\Psr7\\Uri::__construct() must be of the type string, null given, called in C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Request.php on line 44 at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Uri.php:80)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Request.php(44): GuzzleHttp\\Psr7\\Uri->__construct(NULL)
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebtGateway\\Repositories\\Connections\\NextLendServiceConnection.php(143): GuzzleHttp\\Psr7\\Request->__construct('POST', NULL, Array, '{\"Fnc\":\"checkDe...')
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(40): App\\Modules\\CollectDebtGateway\\Repositories\\Connections\\NextLendServiceConnection->buildHttpRequest(Array, 'checkDebt')
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php(56): Illuminate\\Support\\ServiceProvider->{closure}()
#4 [internal function]: GuzzleHttp\\Pool::GuzzleHttp\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\EachPromise.php(81): Generator->rewind()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php(75): GuzzleHttp\\Promise\\EachPromise->promise()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(56): GuzzleHttp\\Pool->promise()
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#35 {main}
"} 
[2025-08-11 18:42:03] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111842_cSnIxx"}} 
[2025-08-11 18:42:03] local.ERROR: Argument 1 passed to GuzzleHttp\Psr7\Uri::__construct() must be of the type string, null given, called in C:\laragon8\www\nextpay-web\api-request-debt\vendor\guzzlehttp\psr7\src\Request.php on line 44 {"exception":"[object] (TypeError(code: 0): Argument 1 passed to GuzzleHttp\\Psr7\\Uri::__construct() must be of the type string, null given, called in C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Request.php on line 44 at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Uri.php:80)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Request.php(44): GuzzleHttp\\Psr7\\Uri->__construct(NULL)
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebtGateway\\Repositories\\Connections\\NextLendServiceConnection.php(143): GuzzleHttp\\Psr7\\Request->__construct('POST', NULL, Array, '{\"Fnc\":\"checkDe...')
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(40): App\\Modules\\CollectDebtGateway\\Repositories\\Connections\\NextLendServiceConnection->buildHttpRequest(Array, 'checkDebt')
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php(56): Illuminate\\Support\\ServiceProvider->{closure}()
#4 [internal function]: GuzzleHttp\\Pool::GuzzleHttp\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\EachPromise.php(81): Generator->rewind()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php(75): GuzzleHttp\\Promise\\EachPromise->promise()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(56): GuzzleHttp\\Pool->promise()
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#35 {main}
"} 
[2025-08-11 18:42:03] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111842_SwdqL9"}} 
[2025-08-11 18:42:03] local.ERROR: Argument 1 passed to GuzzleHttp\Psr7\Uri::__construct() must be of the type string, null given, called in C:\laragon8\www\nextpay-web\api-request-debt\vendor\guzzlehttp\psr7\src\Request.php on line 44 {"exception":"[object] (TypeError(code: 0): Argument 1 passed to GuzzleHttp\\Psr7\\Uri::__construct() must be of the type string, null given, called in C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Request.php on line 44 at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Uri.php:80)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Request.php(44): GuzzleHttp\\Psr7\\Uri->__construct(NULL)
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebtGateway\\Repositories\\Connections\\NextLendServiceConnection.php(143): GuzzleHttp\\Psr7\\Request->__construct('POST', NULL, Array, '{\"Fnc\":\"checkDe...')
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(40): App\\Modules\\CollectDebtGateway\\Repositories\\Connections\\NextLendServiceConnection->buildHttpRequest(Array, 'checkDebt')
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php(56): Illuminate\\Support\\ServiceProvider->{closure}()
#4 [internal function]: GuzzleHttp\\Pool::GuzzleHttp\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\EachPromise.php(81): Generator->rewind()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php(75): GuzzleHttp\\Promise\\EachPromise->promise()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(56): GuzzleHttp\\Pool->promise()
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#35 {main}
"} 
[2025-08-11 18:42:08] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111842_marKPa"}} 
[2025-08-11 18:42:28] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111842_qdQrzH"}} 
[2025-08-11 18:42:28] local.ERROR: Argument 1 passed to GuzzleHttp\Psr7\Uri::__construct() must be of the type string, null given, called in C:\laragon8\www\nextpay-web\api-request-debt\vendor\guzzlehttp\psr7\src\Request.php on line 44 {"exception":"[object] (TypeError(code: 0): Argument 1 passed to GuzzleHttp\\Psr7\\Uri::__construct() must be of the type string, null given, called in C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Request.php on line 44 at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Uri.php:80)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Request.php(44): GuzzleHttp\\Psr7\\Uri->__construct(NULL)
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebtGateway\\Repositories\\Connections\\NextLendServiceConnection.php(143): GuzzleHttp\\Psr7\\Request->__construct('POST', NULL, Array, '{\"Fnc\":\"checkDe...')
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(39): App\\Modules\\CollectDebtGateway\\Repositories\\Connections\\NextLendServiceConnection->buildHttpRequest(Array, 'checkDebt')
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php(56): Illuminate\\Support\\ServiceProvider->{closure}()
#4 [internal function]: GuzzleHttp\\Pool::GuzzleHttp\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\EachPromise.php(81): Generator->rewind()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php(75): GuzzleHttp\\Promise\\EachPromise->promise()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(55): GuzzleHttp\\Pool->promise()
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#35 {main}
"} 
[2025-08-11 18:43:53] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111843_PBwX0c"}} 
[2025-08-11 18:45:00] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111845_iVYdDV"}} 
[2025-08-11 18:45:11] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111845_PIk8Uy"}} 
[2025-08-11 18:45:39] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111845_QWVrI3"}} 
[2025-08-11 18:45:51] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111845_nnWqjL"}} 
[2025-08-11 18:46:03] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111846_Uwcn59"}} 
[2025-08-11 18:46:53] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111846_Plk600"}} 
[2025-08-11 18:46:54] local.ERROR: Call to undefined method GuzzleHttp\Psr7\Response::json() {"exception":"[object] (Error(code: 0): Call to undefined method GuzzleHttp\\Psr7\\Response::json() at C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php:48)
[stacktrace]
#0 [internal function]: Illuminate\\Support\\ServiceProvider->{closure}(Object(GuzzleHttp\\Psr7\\Response), 0, Object(GuzzleHttp\\Promise\\Promise))
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\EachPromise.php(181): call_user_func(Object(Closure), Object(GuzzleHttp\\Psr7\\Response), 0, Object(GuzzleHttp\\Promise\\Promise))
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\Promise.php(204): GuzzleHttp\\Promise\\EachPromise->GuzzleHttp\\Promise\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\Promise.php(153): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(48): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlMultiHandler.php(158): GuzzleHttp\\Promise\\TaskQueue->run()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlMultiHandler.php(183): GuzzleHttp\\Handler\\CurlMultiHandler->tick()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\Promise.php(248): GuzzleHttp\\Handler\\CurlMultiHandler->execute(true)
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\Promise.php(224): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\Promise.php(269): GuzzleHttp\\Promise\\Promise->waitIfPending()
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\Promise.php(226): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\Promise.php(62): GuzzleHttp\\Promise\\Promise->waitIfPending()
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\EachPromise.php(116): GuzzleHttp\\Promise\\Promise->wait()
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\Promise.php(248): GuzzleHttp\\Promise\\EachPromise->GuzzleHttp\\Promise\\{closure}(true)
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\Promise.php(224): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\Promise.php(62): GuzzleHttp\\Promise\\Promise->waitIfPending()
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(57): GuzzleHttp\\Promise\\Promise->wait()
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 {main}
"} 
[2025-08-11 18:46:56] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111846_SWjvat"}} 
[2025-08-11 18:46:57] local.ERROR: Call to undefined method GuzzleHttp\Psr7\Response::json() {"exception":"[object] (Error(code: 0): Call to undefined method GuzzleHttp\\Psr7\\Response::json() at C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php:48)
[stacktrace]
#0 [internal function]: Illuminate\\Support\\ServiceProvider->{closure}(Object(GuzzleHttp\\Psr7\\Response), 0, Object(GuzzleHttp\\Promise\\Promise))
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\EachPromise.php(181): call_user_func(Object(Closure), Object(GuzzleHttp\\Psr7\\Response), 0, Object(GuzzleHttp\\Promise\\Promise))
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\Promise.php(204): GuzzleHttp\\Promise\\EachPromise->GuzzleHttp\\Promise\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\Promise.php(153): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(48): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlMultiHandler.php(158): GuzzleHttp\\Promise\\TaskQueue->run()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlMultiHandler.php(183): GuzzleHttp\\Handler\\CurlMultiHandler->tick()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\Promise.php(248): GuzzleHttp\\Handler\\CurlMultiHandler->execute(true)
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\Promise.php(224): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\Promise.php(269): GuzzleHttp\\Promise\\Promise->waitIfPending()
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\Promise.php(226): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\Promise.php(62): GuzzleHttp\\Promise\\Promise->waitIfPending()
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\EachPromise.php(116): GuzzleHttp\\Promise\\Promise->wait()
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\Promise.php(248): GuzzleHttp\\Promise\\EachPromise->GuzzleHttp\\Promise\\{closure}(true)
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\Promise.php(224): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\Promise.php(62): GuzzleHttp\\Promise\\Promise->waitIfPending()
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\test.php(57): GuzzleHttp\\Promise\\Promise->wait()
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 {main}
"} 
[2025-08-11 18:47:42] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111847_aFY3t7"}} 
[2025-08-11 18:47:57] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111847_aD8n10"}} 
[2025-08-11 18:48:24] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111848_F88ub5"}} 
[2025-08-11 18:48:31] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111848_UH7gmj"}} 
[2025-08-11 18:49:22] local.INFO: CheckRedis {"request":{"api_request_id":"DEBT_08111849_0pOJFW"}} 
[2025-08-12 08:29:05] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120829_BBXMsN"}} 
[2025-08-12 08:29:08] local.ERROR: Argument 1 passed to GuzzleHttp\Psr7\Uri::__construct() must be of the type string, null given, called in C:\laragon8\www\nextpay-web\api-request-debt\vendor\guzzlehttp\psr7\src\Request.php on line 44 {"exception":"[object] (TypeError(code: 0): Argument 1 passed to GuzzleHttp\\Psr7\\Uri::__construct() must be of the type string, null given, called in C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Request.php on line 44 at C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Uri.php:80)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\psr7\\src\\Request.php(44): GuzzleHttp\\Psr7\\Uri->__construct(NULL)
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebtGateway\\Repositories\\Connections\\NextLendServiceConnection.php(141): GuzzleHttp\\Psr7\\Request->__construct('POST', NULL, Array, '{\"Fnc\":\"checkDe...')
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestCheckPaymentAction\\DebtRecoveryRequestCheckPaymentAction.php(63): App\\Modules\\CollectDebtGateway\\Repositories\\Connections\\NextLendServiceConnection->buildHttpRequest(Array, 'checkDebt')
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php(56): App\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestCheckPaymentAction\\DebtRecoveryRequestCheckPaymentAction->App\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestCheckPaymentAction\\{closure}()
#4 [internal function]: GuzzleHttp\\Pool::GuzzleHttp\\{closure}()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\promises\\src\\EachPromise.php(81): Generator->rewind()
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\guzzlehttp\\guzzle\\src\\Pool.php(75): GuzzleHttp\\Promise\\EachPromise->promise()
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestCheckPaymentAction\\DebtRecoveryRequestCheckPaymentAction.php(80): GuzzleHttp\\Pool->promise()
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(103): App\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestCheckPaymentAction\\DebtRecoveryRequestCheckPaymentAction->App\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestCheckPaymentAction\\{closure}(Object(Illuminate\\Database\\Eloquent\\Collection))
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestCheckPaymentAction\\DebtRecoveryRequestCheckPaymentAction.php(82): Illuminate\\Database\\Eloquent\\Builder->chunkById(30, Object(Closure))
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Controllers\\v1\\CollectDebtRequest\\CollectDebtRequestController.php(166): App\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestCheckPaymentAction\\DebtRecoveryRequestCheckPaymentAction->run(Object(Illuminate\\Http\\Request))
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Modules\\CollectDebt\\Controllers\\v1\\CollectDebtRequest\\CollectDebtRequestController->checkPayment(Object(Illuminate\\Http\\Request))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('checkPayment', Array)
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(239): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\CollectDebt\\Controllers\\v1\\CollectDebtRequest\\CollectDebtRequestController), 'checkPayment')
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(196): Illuminate\\Routing\\Route->runController()
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\EnableSettingMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\EnableSettingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'JOB_KIEM_TRA_KE...')
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#41 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#42 {main}
"} 
[2025-08-12 08:30:42] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120830_kasNDy"}} 
[2025-08-12 08:31:07] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120831_lVt50O"}} 
[2025-08-12 08:31:24] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120831_x5pO4J"}} 
[2025-08-12 08:31:28] local.ERROR: Call to a member function pluck() on null {"exception":"[object] (Error(code: 0): Call to a member function pluck() on null at C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Controllers\\v1\\CollectDebtRequest\\CollectDebtRequestController.php:168)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Modules\\CollectDebt\\Controllers\\v1\\CollectDebtRequest\\CollectDebtRequestController->checkPayment(Object(Illuminate\\Http\\Request))
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('checkPayment', Array)
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(239): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\CollectDebt\\Controllers\\v1\\CollectDebtRequest\\CollectDebtRequestController), 'checkPayment')
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(196): Illuminate\\Routing\\Route->runController()
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\EnableSettingMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\EnableSettingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'JOB_KIEM_TRA_KE...')
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#31 {main}
"} 
[2025-08-12 08:31:57] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120831_jPgHIB"}} 
[2025-08-12 08:36:29] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120836_ZeWDW9"}} 
[2025-08-12 08:38:52] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120838_CJIvQp"}} 
[2025-08-12 08:40:34] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120840_GHp9hm"}} 
[2025-08-12 08:41:26] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120841_neyEbR"}} 
[2025-08-12 08:45:50] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120845_gCFUQ1"}} 
[2025-08-12 08:49:45] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120849_VjriW0"}} 
[2025-08-12 08:50:33] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120850_3yIgsP"}} 
[2025-08-12 08:54:14] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120854_LDD0iY"}} 
[2025-08-12 08:54:21] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120854_LeAaZk"}} 
[2025-08-12 08:54:28] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120854_S3Iijb"}} 
[2025-08-12 08:54:38] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120854_g2wvTu"}} 
[2025-08-12 08:54:49] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120854_mhCJdI"}} 
[2025-08-12 08:55:30] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120855_5MC5dW"}} 
[2025-08-12 08:55:53] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120855_tPQIfs"}} 
[2025-08-12 08:56:20] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120856_GyHzTL"}} 
[2025-08-12 08:58:27] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120858_hEKdJH"}} 
[2025-08-12 09:00:52] local.INFO: DebtRecoveryRequestCheckPayment {"request":{"api_request_id":"DEBT_08120900_LfNC06"}} 
