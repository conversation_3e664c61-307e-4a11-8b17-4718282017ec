<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction;

use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebtGateway\Repositories\Connections\NextLendServiceConnection;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;

class DebtRecoveryRequestCheckPaymentAction
{
	private int $__limit = 30;

	public NextLendServiceConnection $nextlendServiceCon;

	public function __construct(NextlendServiceConnection $nextlendServiceCon)
	{
		$this->nextlendServiceCon = $nextlendServiceCon;
	}

	public function run()
	{
		$client = new Client([
			'base_uri' => $this->nextlendServiceCon->_API_URL,
			'timeout' => 10,
			'verify' => false
		]);


		$whereRaw = sprintf(
			"payment_method_code = 'MPOS' AND (status = %s OR status = %s) 
                                    AND status_payment = %s 
                                    AND time_receivered IS NULL 
                                    AND (time_checked IS NULL OR time_checked + %d < %d)",

			CollectDebtEnum::REQUEST_STT_MOI_TAO,
			CollectDebtEnum::REQUEST_STT_DA_DUYET,

			CollectDebtEnum::REQUEST_STT_PM_DA_GUI,

			env('THOI_GIAN_CHECK_LAI_YEU_CAU_TRICH', 30) * 60,
			time()
		);

		CollectDebtRequest::query()
			->with('collectDebtPartner')
			->whereRaw($whereRaw)
			->chunkById($this->__limit, function (Collection $collectDebtRequests) use ($client) {

				// Generator 
				$requests = function () use ($collectDebtRequests) {
					foreach ($collectDebtRequests as $rq) {
						$input = app(CheckRequestViaMposSubAction::class)->buildParamsDirect($rq);
						yield $rq => $this->nextlendServiceCon->buildHttpRequest($input, 'checkDebt');
					}
				};

				$pool = new Pool($client, $requests(), [
					'concurrency' => 5,
					'fulfilled' => function ($response, CollectDebtRequest $item) {

						$body = (string)$response->getBody();
						$result = json_decode($body, true);

						$this->handleCheckResult($item, $result);
					},
					'rejected' => function (RequestException $reason, CollectDebtRequest $item) {
						echo "✗ #$item->partner_request_id failed: " . $reason->getMessage() . "\n";
					},
				]);

				$promise = $pool->promise();
				$promise->wait();
			});
	}

	public function handleCheckResult(CollectDebtRequest $collectDebtRequest, $checkDebtResult=[], bool $isCutOff=false) {
		if ($collectDebtRequest->collectDebtPartner) {
			$collectDebtRequest->forceFill(['time_checked' => time()])->update();
			return;
		}

		if (!isset($checkDebtResult['RespCode']) || empty($checkDebtResult['data'])) {
			$collectDebtRequest->forceFill(['time_checked' => time()])->update();
			return;
		}

		// Có response code
		$nextlendServiceData = json_decode($checkDebtResult['data'], true);
		$mposResultData = json_decode($nextlendServiceData['data'], true);
		
		// Check bị lỗi
		if ( !isset($mposResultData['status']) ) {
			$collectDebtRequest->forceFill(['time_checked' => time()])->update();
			return;
		}

		// Check có thông tin
		$mposDebtStatus = $mposResultData['data']['debtStatus'];

		switch ($mposDebtStatus) {
			case 'PENDING':
				$collectDebtRequest->forceFill(['time_checked' => time()])->update();
				return;

			case 'APPROVED':
				$amountSuccess = $mposResultData['data']['debtRecoveryAmount'];
				break;

			case 'CANCEL':
			case 'EXPIRED':
				$amountSuccess = 0;
				break;

			case 'TIMEOUT':
				// sẽ call lại
				return;


			default:
				$collectDebtRequest->forceFill(['time_checked' => time()])->update();
				return;
		}
	}
}  // End classi\