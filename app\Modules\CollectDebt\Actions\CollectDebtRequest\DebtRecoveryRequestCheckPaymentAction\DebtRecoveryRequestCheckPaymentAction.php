<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction;

use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebtGateway\Repositories\Connections\NextLendServiceConnection;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;

class DebtRecoveryRequestCheckPaymentAction
{
	private const CHUNK_LIMIT = 30;
	private const POOL_CONCURRENCY = 5;
	private const HTTP_TIMEOUT = 10;

	public NextLendServiceConnection $nextlendServiceCon;

	public function __construct(NextlendServiceConnection $nextlendServiceCon)
	{
		$this->nextlendServiceCon = $nextlendServiceCon;
	}

	public function run()
	{
		$client = $this->createHttpClient();
		$whereRaw = $this->buildWhereCondition();

		CollectDebtRequest::query()
			->with('collectDebtPartner')
			->whereRaw($whereRaw)
			->chunkById(self::CHUNK_LIMIT, function (Collection $collectDebtRequests) use ($client) {
				$this->processRequestsBatch($client, $collectDebtRequests);
			});
	}

	private function createHttpClient(): Client
	{
		return new Client([
			'base_uri' => $this->nextlendServiceCon->_API_URL,
			'timeout' => self::HTTP_TIMEOUT,
			'verify' => false
		]);
	}

	private function buildWhereCondition(): string
	{
		return sprintf(
			"payment_method_code = 'MPOS' AND (status = %s OR status = %s)
                                    AND status_payment = %s
                                   ",
			CollectDebtEnum::REQUEST_STT_MOI_TAO,
			CollectDebtEnum::REQUEST_STT_DA_DUYET,
			CollectDebtEnum::REQUEST_STT_PM_DA_GUI,
			env('THOI_GIAN_CHECK_LAI_YEU_CAU_TRICH', 30) * 60,
			time()
		);
	}

	private function processRequestsBatch(Client $client, Collection $collectDebtRequests): void
	{
		// Generator
		$requests = function () use ($collectDebtRequests) {
			foreach ($collectDebtRequests as $rq) {
				$input = app(CheckRequestViaMposSubAction::class)->buildParamsDirect($rq);
				yield $rq => $this->nextlendServiceCon->buildHttpRequest($input, 'checkDebt');
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => self::POOL_CONCURRENCY,
			'fulfilled' => function ($response, CollectDebtRequest $item) {
				$body = (string)$response->getBody();
				$result = json_decode($body, true);
				$this->handleCheckResult($item, $result);
			},
			'rejected' => function (RequestException $reason, CollectDebtRequest $item) {
				echo "✗ #$item->partner_request_id failed: " . $reason->getMessage() . "\n";
			},
		]);

		$promise = $pool->promise();
		$promise->wait();
	}

	public function handleCheckResult(CollectDebtRequest $collectDebtRequest, $checkDebtResult = [])
	{
		// có partner
		if ($collectDebtRequest->collectDebtPartner) {
			$this->updateTimeChecked($collectDebtRequest);
			return;
		}

		// không có kết quả check
		if (empty($checkDebtResult['RespCode']) || empty($checkDebtResult['data'])) {
			$this->updateTimeChecked($collectDebtRequest);
			return;
		}

		// Có response code
		$nextlendServiceData = json_decode($checkDebtResult['data'], true);
		$mposResultData = json_decode($nextlendServiceData['data'], true);

		// mpos trả kết quả check bị lỗi
		if (empty($mposResultData['status'])) {
			$this->updateTimeChecked($collectDebtRequest);
			return;
		}

		// Check có thông tin
		$mposDebtStatus = $mposResultData['data']['debtStatus'];

		switch ($mposDebtStatus) {
			case 'PENDING':
				$this->updateTimeChecked($collectDebtRequest);
				return;

			case 'APPROVED':
				// Logic xử lý khi approved - giữ nguyên như hiện tại
				$amountSuccess = $mposResultData['data']['debtRecoveryAmount'];
				break;

			case 'CANCEL':
			case 'EXPIRED':
				// Logic xử lý khi cancel/expired - giữ nguyên như hiện tại
				$amountSuccess = 0;
				break;

			case 'TIMEOUT':
				// sẽ call lại
				return;

			default:
				$this->updateTimeChecked($collectDebtRequest);
				return;
		}
	}

	private function updateTimeChecked(CollectDebtRequest $collectDebtRequest): void
	{
		$collectDebtRequest->forceFill(['time_checked' => time()])->update();
	}
}  // End classi\